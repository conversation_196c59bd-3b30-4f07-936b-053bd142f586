package com.neogenperformance.boxx.widget

import org.junit.Test
import org.junit.Assert.*

/**
 * Test class for NeogenBossWidget
 */
class NeogenBossWidgetTest {
    
    @Test
    fun widget_provider_is_not_null() {
        val provider = NeogenBossWidgetProvider()
        assertNotNull("Widget provider should not be null", provider)
        assertNotNull("Glance app widget should not be null", provider.glanceAppWidget)
    }
    
    @Test
    fun widget_is_instance_of_correct_class() {
        val provider = NeogenBossWidgetProvider()
        assertTrue("Widget should be instance of NeogenBossWidget", 
                   provider.glanceAppWidget is NeogenBossWidget)
    }
}
